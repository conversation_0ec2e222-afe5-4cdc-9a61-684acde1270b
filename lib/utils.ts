import { Category } from '@/types';

export const defaultCategories: Category[] = [
  // 收入分类
  { id: '1', name: '工资', type: 'income', color: '#10B981', icon: '💰' },
  { id: '2', name: '奖金', type: 'income', color: '#059669', icon: '🎁' },
  { id: '3', name: '投资收益', type: 'income', color: '#047857', icon: '📈' },
  { id: '4', name: '其他收入', type: 'income', color: '#065F46', icon: '💵' },
  
  // 支出分类
  { id: '5', name: '餐饮', type: 'expense', color: '#EF4444', icon: '🍽️' },
  { id: '6', name: '交通', type: 'expense', color: '#DC2626', icon: '🚗' },
  { id: '7', name: '购物', type: 'expense', color: '#B91C1C', icon: '🛍️' },
  { id: '8', name: '娱乐', type: 'expense', color: '#991B1B', icon: '🎮' },
  { id: '9', name: '医疗', type: 'expense', color: '#7F1D1D', icon: '🏥' },
  { id: '10', name: '教育', type: 'expense', color: '#F59E0B', icon: '📚' },
  { id: '11', name: '住房', type: 'expense', color: '#D97706', icon: '🏠' },
  { id: '12', name: '其他支出', type: 'expense', color: '#92400E', icon: '💸' },
];

export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 2,
  }).format(amount);
};

export const formatDate = (date: string): string => {
  return new Date(date).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  });
};

export const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};