#!/bin/bash

# 创建打包脚本 - 只包含Docker构建所需的最小文件集
# 作者: AI Assistant
# 用途: 将项目的最小必需文件打包，用于Docker构建

echo "🚀 开始打包最小文件集..."

# 创建临时目录
TEMP_DIR="money-tracker-minimal"
PACKAGE_NAME="money-tracker-minimal.tar.gz"

# 如果临时目录存在，先删除
if [ -d "$TEMP_DIR" ]; then
    rm -rf "$TEMP_DIR"
fi

# 创建临时目录
mkdir -p "$TEMP_DIR"

echo "📁 复制必需的配置文件..."
# 复制根目录的配置文件
cp package.json "$TEMP_DIR/"
cp package-lock.json "$TEMP_DIR/"
cp next.config.js "$TEMP_DIR/"
cp tsconfig.json "$TEMP_DIR/"
cp tailwind.config.js "$TEMP_DIR/"
cp postcss.config.js "$TEMP_DIR/"
cp next-env.d.ts "$TEMP_DIR/"
cp Dockerfile "$TEMP_DIR/"
cp .dockerignore "$TEMP_DIR/"

echo "📂 复制源代码目录..."
# 复制源代码目录
cp -r app "$TEMP_DIR/"
cp -r components "$TEMP_DIR/"
cp -r lib "$TEMP_DIR/"
cp -r types "$TEMP_DIR/"

echo "📦 创建压缩包..."
# 创建tar.gz压缩包
tar -czf "$PACKAGE_NAME" "$TEMP_DIR"

# 清理临时目录
rm -rf "$TEMP_DIR"

# 显示结果
echo "✅ 打包完成！"
echo "📄 文件名: $PACKAGE_NAME"
echo "📊 文件大小: $(du -h "$PACKAGE_NAME" | cut -f1)"
echo ""
echo "📋 包含的文件:"
echo "   配置文件: package.json, package-lock.json, next.config.js, tsconfig.json, tailwind.config.js, postcss.config.js, next-env.d.ts"
echo "   Docker文件: Dockerfile, .dockerignore"
echo "   源代码: app/, components/, lib/, types/"
echo ""
echo "🚀 使用方法:"
echo "   1. 上传 $PACKAGE_NAME 到你的Docker构建环境"
echo "   2. 解压: tar -xzf $PACKAGE_NAME"
echo "   3. 进入目录: cd $TEMP_DIR"
echo "   4. 构建镜像: docker build -t money-tracker ."