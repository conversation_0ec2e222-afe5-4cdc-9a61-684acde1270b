'use client';

import { useState, useEffect } from 'react';
import { Transaction, Category } from '@/types';
import { defaultCategories, formatCurrency, formatDate, generateId } from '@/lib/utils';
import { Plus, TrendingUp, TrendingDown, Wallet, BarChart3 } from 'lucide-react';
import TransactionForm from '@/components/TransactionForm';
import TransactionList from '@/components/TransactionList';
import StatsCard from '@/components/StatsCard';
import CategoryChart from '@/components/CategoryChart';

export default function Home() {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [categories] = useState<Category[]>(defaultCategories);
  const [showForm, setShowForm] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'transactions' | 'stats'>('overview');

  // 从localStorage加载数据
  useEffect(() => {
    const savedTransactions = localStorage.getItem('transactions');
    if (savedTransactions) {
      setTransactions(JSON.parse(savedTransactions));
    }
  }, []);

  // 保存数据到localStorage
  useEffect(() => {
    localStorage.setItem('transactions', JSON.stringify(transactions));
  }, [transactions]);

  const addTransaction = (transaction: Omit<Transaction, 'id' | 'createdAt'>) => {
    const newTransaction: Transaction = {
      ...transaction,
      id: generateId(),
      createdAt: new Date().toISOString(),
    };
    setTransactions(prev => [newTransaction, ...prev]);
    setShowForm(false);
  };

  const deleteTransaction = (id: string) => {
    setTransactions(prev => prev.filter(t => t.id !== id));
  };

  // 计算统计数据
  const totalIncome = transactions
    .filter(t => t.type === 'income')
    .reduce((sum, t) => sum + t.amount, 0);

  const totalExpense = transactions
    .filter(t => t.type === 'expense')
    .reduce((sum, t) => sum + t.amount, 0);

  const balance = totalIncome - totalExpense;

  return (
    <div className="max-w-4xl mx-auto p-4">
      {/* 头部 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">记账本</h1>
        <p className="text-gray-600">管理你的收入和支出</p>
      </div>

      {/* 导航标签 */}
      <div className="flex space-x-1 mb-6 bg-gray-100 p-1 rounded-lg">
        <button
          onClick={() => setActiveTab('overview')}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'overview'
              ? 'bg-white text-gray-900 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          概览
        </button>
        <button
          onClick={() => setActiveTab('transactions')}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'transactions'
              ? 'bg-white text-gray-900 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          交易记录
        </button>
        <button
          onClick={() => setActiveTab('stats')}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'stats'
              ? 'bg-white text-gray-900 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          统计分析
        </button>
      </div>

      {/* 概览页面 */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* 统计卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <StatsCard
              title="总收入"
              amount={totalIncome}
              icon={<TrendingUp className="h-6 w-6 text-green-600" />}
              color="green"
            />
            <StatsCard
              title="总支出"
              amount={totalExpense}
              icon={<TrendingDown className="h-6 w-6 text-red-600" />}
              color="red"
            />
            <StatsCard
              title="余额"
              amount={balance}
              icon={<Wallet className="h-6 w-6 text-blue-600" />}
              color={balance >= 0 ? 'green' : 'red'}
            />
          </div>

          {/* 最近交易 */}
          <div className="card">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">最近交易</h2>
              <button
                onClick={() => setActiveTab('transactions')}
                className="text-primary-600 hover:text-primary-700 text-sm font-medium"
              >
                查看全部
              </button>
            </div>
            <TransactionList
              transactions={transactions.slice(0, 5)}
              categories={categories}
              onDelete={deleteTransaction}
            />
          </div>
        </div>
      )}

      {/* 交易记录页面 */}
      {activeTab === 'transactions' && (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">交易记录</h2>
            <button
              onClick={() => setShowForm(true)}
              className="btn btn-primary px-4 py-2 flex items-center space-x-2"
            >
              <Plus className="h-4 w-4" />
              <span>添加交易</span>
            </button>
          </div>

          <div className="card">
            <TransactionList
              transactions={transactions}
              categories={categories}
              onDelete={deleteTransaction}
            />
          </div>
        </div>
      )}

      {/* 统计分析页面 */}
      {activeTab === 'stats' && (
        <div className="space-y-6">
          <h2 className="text-xl font-semibold text-gray-900">统计分析</h2>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <CategoryChart
              transactions={transactions}
              categories={categories}
              type="expense"
              title="支出分类"
            />
            <CategoryChart
              transactions={transactions}
              categories={categories}
              type="income"
              title="收入分类"
            />
          </div>
        </div>
      )}

      {/* 添加交易按钮 (移动端) */}
      {activeTab !== 'transactions' && (
        <button
          onClick={() => setShowForm(true)}
          className="fixed bottom-6 right-6 bg-primary-600 hover:bg-primary-700 text-white p-4 rounded-full shadow-lg transition-colors md:hidden"
        >
          <Plus className="h-6 w-6" />
        </button>
      )}

      {/* 交易表单模态框 */}
      {showForm && (
        <TransactionForm
          categories={categories}
          onSubmit={addTransaction}
          onClose={() => setShowForm(false)}
        />
      )}
    </div>
  );
}