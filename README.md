# 记账本 - Next.js 记账应用

一个简单易用的个人记账应用，使用 Next.js 14 和 TypeScript 构建。

## 功能特性

- ✅ 添加收入和支出记录
- ✅ 分类管理（预设多种分类）
- ✅ 数据统计和图表展示
- ✅ 响应式设计，支持移动端
- ✅ 本地存储，数据持久化
- ✅ 现代化 UI 设计

## 技术栈

- **框架**: Next.js 14 (App Router)
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **图标**: Lucide React
- **图表**: Recharts
- **日期处理**: date-fns

## 快速开始

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看应用。

### 构建生产版本

```bash
npm run build
npm start
```

## 项目结构

```
├── app/                    # Next.js App Router
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 主页面
├── components/            # React 组件
│   ├── CategoryChart.tsx  # 分类图表
│   ├── StatsCard.tsx      # 统计卡片
│   ├── TransactionForm.tsx # 交易表单
│   └── TransactionList.tsx # 交易列表
├── lib/                   # 工具函数
│   └── utils.ts           # 通用工具
├── types/                 # TypeScript 类型定义
│   └── index.ts           # 类型定义
└── public/                # 静态资源
```

## 使用说明

1. **添加交易**: 点击"添加交易"按钮，选择收入或支出类型，填写金额、分类和描述
2. **查看记录**: 在"交易记录"页面查看所有交易历史
3. **统计分析**: 在"统计分析"页面查看收支分类图表
4. **数据管理**: 所有数据保存在浏览器本地存储中

## 预设分类

### 收入分类
- 💰 工资
- 🎁 奖金  
- 📈 投资收益
- 💵 其他收入

### 支出分类
- 🍽️ 餐饮
- 🚗 交通
- 🛍️ 购物
- 🎮 娱乐
- 🏥 医疗
- 📚 教育
- 🏠 住房
- 💸 其他支出

## 开发

### 添加新功能

1. 在 `types/index.ts` 中定义新的类型
2. 在 `components/` 中创建新组件
3. 在 `lib/utils.ts` 中添加工具函数
4. 更新主页面 `app/page.tsx`

### 自定义样式

项目使用 Tailwind CSS，可以在 `tailwind.config.js` 中自定义主题。

## 许可证

MIT License