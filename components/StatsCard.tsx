import { formatCurrency } from '@/lib/utils';

interface StatsCardProps {
  title: string;
  amount: number;
  icon: React.ReactNode;
  color: 'green' | 'red' | 'blue';
}

export default function StatsCard({ title, amount, icon, color }: StatsCardProps) {
  const colorClasses = {
    green: 'bg-green-50 border-green-200',
    red: 'bg-red-50 border-red-200',
    blue: 'bg-blue-50 border-blue-200',
  };

  const textColorClasses = {
    green: 'text-green-600',
    red: 'text-red-600',
    blue: 'text-blue-600',
  };

  return (
    <div className={`card border ${colorClasses[color]}`}>
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className={`text-2xl font-bold ${textColorClasses[color]}`}>
            {formatCurrency(amount)}
          </p>
        </div>
        <div className="flex-shrink-0">
          {icon}
        </div>
      </div>
    </div>
  );
}