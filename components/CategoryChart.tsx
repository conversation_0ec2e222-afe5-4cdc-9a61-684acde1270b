'use client';

import { Transaction, Category } from '@/types';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Legend, Tooltip } from 'recharts';

interface CategoryChartProps {
  transactions: Transaction[];
  categories: Category[];
  type: 'income' | 'expense';
  title: string;
}

export default function CategoryChart({ transactions, categories, type, title }: CategoryChartProps) {
  // 过滤指定类型的交易
  const filteredTransactions = transactions.filter(t => t.type === type);
  
  // 按分类统计金额
  const categoryStats = categories
    .filter(cat => cat.type === type)
    .map(category => {
      const categoryTransactions = filteredTransactions.filter(t => t.category === category.name);
      const total = categoryTransactions.reduce((sum, t) => sum + t.amount, 0);
      
      return {
        name: category.name,
        value: total,
        color: category.color,
        icon: category.icon,
      };
    })
    .filter(stat => stat.value > 0)
    .sort((a, b) => b.value - a.value);

  if (categoryStats.length === 0) {
    return (
      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
        <div className="text-center py-8 text-gray-500">
          <p>暂无{type === 'income' ? '收入' : '支出'}数据</p>
        </div>
      </div>
    );
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 0,
    }).format(value);
  };

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border rounded-lg shadow-lg">
          <p className="font-medium">{data.icon} {data.name}</p>
          <p className="text-sm text-gray-600">{formatCurrency(data.value)}</p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="card">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
      
      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={categoryStats}
              cx="50%"
              cy="50%"
              innerRadius={40}
              outerRadius={80}
              paddingAngle={2}
              dataKey="value"
            >
              {categoryStats.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
          </PieChart>
        </ResponsiveContainer>
      </div>

      {/* 图例 */}
      <div className="mt-4 space-y-2">
        {categoryStats.slice(0, 5).map((stat, index) => (
          <div key={stat.name} className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-2">
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: stat.color }}
              />
              <span>{stat.icon} {stat.name}</span>
            </div>
            <span className="font-medium">{formatCurrency(stat.value)}</span>
          </div>
        ))}
        {categoryStats.length > 5 && (
          <div className="text-xs text-gray-500 text-center pt-2">
            还有 {categoryStats.length - 5} 个分类...
          </div>
        )}
      </div>
    </div>
  );
}