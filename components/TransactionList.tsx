'use client';

import { Transaction, Category } from '@/types';
import { formatCurrency, formatDate } from '@/lib/utils';
import { Trash2 } from 'lucide-react';

interface TransactionListProps {
  transactions: Transaction[];
  categories: Category[];
  onDelete: (id: string) => void;
}

export default function TransactionList({ transactions, categories, onDelete }: TransactionListProps) {
  if (transactions.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <p>暂无交易记录</p>
      </div>
    );
  }

  const getCategoryInfo = (categoryName: string) => {
    return categories.find(cat => cat.name === categoryName);
  };

  return (
    <div className="space-y-2">
      {transactions.map(transaction => {
        const categoryInfo = getCategoryInfo(transaction.category);
        
        return (
          <div
            key={transaction.id}
            className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <div className="flex items-center space-x-3">
              <div className="text-2xl">
                {categoryInfo?.icon || '💰'}
              </div>
              <div>
                <div className="font-medium text-gray-900">
                  {transaction.description || transaction.category}
                </div>
                <div className="text-sm text-gray-500">
                  {transaction.category} • {formatDate(transaction.date)}
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className={`font-semibold ${
                transaction.type === 'income' ? 'text-green-600' : 'text-red-600'
              }`}>
                {transaction.type === 'income' ? '+' : '-'}{formatCurrency(transaction.amount)}
              </div>
              <button
                onClick={() => onDelete(transaction.id)}
                className="text-gray-400 hover:text-red-600 transition-colors"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            </div>
          </div>
        );
      })}
    </div>
  );
}